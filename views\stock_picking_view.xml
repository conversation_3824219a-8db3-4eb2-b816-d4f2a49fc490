<odoo>
    <record id="view_stock_picking_inherit_view" model="ir.ui.view">
        <field name="name">view.stock.picking.inherit.view</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <xpath expr="///field[@name='date_done']" position="after">
                <button type="object" class="fa fa-arrow-right oe_link" name="update_date_done" string="Update Effective Date" groups="eg_stock_edit_date_done.update_effective_date"
                invisible="state not in ['done','cancel']"/>
            </xpath>
        </field>
    </record>
</odoo>