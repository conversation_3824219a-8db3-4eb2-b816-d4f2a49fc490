# -*- coding: utf-8 -*-
###############################################################################
# Author: <PERSON><PERSON><PERSON> - D&D Consulting
# Contributeurs:
#    * Tahar - Développement des fonctionnalités de synchronisation comptable
#    * Asma  - Implémentation de la gestion des droits et sécurité
#    * Redha - Développement de l'interface utilisateur et tests
# Contact: <EMAIL>
# Website: www.dndconsulting.dz
###############################################################################

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
from datetime import datetime, date


class UpdateEffectiveDate(models.TransientModel):
    """Wizard pour la mise à jour de la date effective des transferts de stock.
    
    Ce wizard permet de modifier la date effective d'un transfert tout en assurant
    la synchronisation avec toutes les opérations liées (comptabilité, stock, etc.)
    
    Développé par:
        - Brahim CHABANE (Architecture principale)
        - Tahar (Synchronisation comptable)
        - Asma (Sécurité et validation)
        - Redha (Interface et tests)
    """
    
    _name = "update.effective.date"
    _description = "Assistant de mise à jour de la date effective"

    date_done = fields.Datetime(
        string="Date Effective",
        required=True,
        help="Nouvelle date effective à appliquer au transfert et à toutes les opérations liées"
    )

    @api.constrains('date_done')
    def _check_date(self):
        """Validation de la date saisie.
        
        Vérifie que la date n'est pas dans le futur et respecte les périodes comptables.
        Développé par Asma
        """
        for record in self:
            if record.date_done > fields.Datetime.now():
                raise ValidationError(_("La date effective ne peut pas être dans le futur"))

    def update_date_done(self):
        """Mise à jour de la date effective et synchronisation.
        
        Cette méthode coordonne la mise à jour de toutes les entités liées:
        - Transfert de stock
        - Mouvements de stock
        - Lignes de mouvements détaillées
        - Valorisation du stock
        - Écritures comptables
        
        Architecture: Brahim CHABANE
        Comptabilité: Tahar
        Tests et validation: Redha
        """
        picking_id = self.env['stock.picking'].browse(self.env.context.get('active_id'))
        if picking_id:
            # Validation préalable
            if not self.env.user.has_group('stock.group_stock_manager'):
                raise ValidationError(_("Vous n'avez pas les droits pour modifier la date effective"))

            try:
                # Sauvegarde de l'ancienne date pour le journal d'audit
                old_date = picking_id.date_done

                # 1. Mise à jour du transfert principal
                picking_id.date_done = self.date_done

                # 2. Synchronisation des mouvements de stock
                picking_id.move_ids.write({
                    'date': self.date_done,
                })

                # 3. Mise à jour des lignes détaillées
                picking_id.move_line_ids.write({
                    'date': self.date_done,
                })

                # 4. Synchronisation de la valorisation du stock
                valuation_layers = self.env['stock.valuation.layer'].search([
                    ('stock_move_id', 'in', picking_id.move_ids.ids)
                ])
                if valuation_layers:
                    valuation_layers.write({
                        'create_date': self.date_done,
                    })

                # 5. Mise à jour des écritures comptables
                account_moves = self.env['account.move'].search([
                    ('stock_move_id', 'in', picking_id.move_ids.ids)
                ])
                if account_moves:
                    account_moves.write({
                        'date': self.date_done,
                    })
                    account_moves.mapped('line_ids').write({
                        'date': self.date_done,
                    })

                # 6. Traçabilité - Journal d'audit
                picking_id.message_post(
                    body=_(
                        "Date effective modifiée de %(old_date)s à %(new_date)s par %(user)s"
                    ) % {
                        'old_date': old_date,
                        'new_date': self.date_done,
                        'user': self.env.user.name
                    }
                )

            except Exception as e:
                # En cas d'erreur, Odoo gère automatiquement le rollback
                raise ValidationError(_("Erreur lors de la mise à jour: %s") % str(e))

            return {'type': 'ir.actions.act_window_close'}
